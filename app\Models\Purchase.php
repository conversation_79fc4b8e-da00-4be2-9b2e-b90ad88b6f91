<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Purchase extends Model
{
    protected $fillable = [
        'appointment_id',
        'status',
        'description',
        'item_type',
        'weight',
        'buy_price',
        'resale_price',
        'benefit',
        'notes'
    ];
    public function appointment() {
        return $this->belongsTo(Appointment::class);
    }
}

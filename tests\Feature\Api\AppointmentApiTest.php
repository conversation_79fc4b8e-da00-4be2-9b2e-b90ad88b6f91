<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Appointment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AppointmentApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->assistant = User::factory()->create([
            'role' => 'assistant',
            'is_activated' => true,
        ]);
        
        $this->representative = User::factory()->create([
            'role' => 'representative',
            'is_activated' => true,
        ]);
        
        $this->token = $this->assistant->createToken('test-token')->plainTextToken;
    }

    public function test_can_get_appointments_list()
    {
        Appointment::factory()->count(3)->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/appointments');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'assistant_id',
                            'representative_id',
                            'client_name',
                            'client_phone',
                            'client_address',
                            'source',
                            'date_time',
                            'notes',
                            'items_collection',
                            'appointment_type',
                        ],
                    ],
                ]);
    }

    public function test_can_create_appointment()
    {
        $appointmentData = [
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
            'client_name' => 'John Doe',
            'client_phone' => '1234567890',
            'client_address' => '123 Main St',
            'source' => 'outbound',
            'date_time' => now()->addDay()->toISOString(),
            'notes' => 'Test appointment',
            'items_collection' => ['bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)'],
            'appointment_type' => 'announced',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/appointments', $appointmentData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'assistant_id',
                        'representative_id',
                        'client_name',
                        'client_phone',
                        'client_address',
                        'source',
                        'date_time',
                        'notes',
                        'items_collection',
                        'appointment_type',
                    ],
                ]);

        $this->assertDatabaseHas('appointments', [
            'clientName' => 'John Doe',
            'clientPhone' => '1234567890',
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);
    }

    public function test_can_get_single_appointment()
    {
        $appointment = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson("/api/v1/appointments/{$appointment->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'assistant_id',
                        'representative_id',
                        'client_name',
                        'client_phone',
                        'client_address',
                        'source',
                        'date_time',
                        'notes',
                        'items_collection',
                        'appointment_type',
                        'assistant',
                        'representative',
                    ],
                ]);
    }

    public function test_can_update_appointment()
    {
        $appointment = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $updateData = [
            'client_name' => 'Updated Name',
            'notes' => 'Updated notes',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson("/api/v1/appointments/{$appointment->id}", $updateData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Appointment updated successfully',
                ]);

        $this->assertDatabaseHas('appointments', [
            'id' => $appointment->id,
            'clientName' => 'Updated Name',
            'notes' => 'Updated notes',
        ]);
    }

    public function test_can_delete_appointment()
    {
        $appointment = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->deleteJson("/api/v1/appointments/{$appointment->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Appointment deleted successfully',
                ]);

        $this->assertDatabaseMissing('appointments', [
            'id' => $appointment->id,
        ]);
    }

    public function test_can_get_my_appointments()
    {
        // Create appointments for this assistant
        Appointment::factory()->count(2)->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        // Create appointment for another assistant
        $otherAssistant = User::factory()->create(['role' => 'assistant']);
        Appointment::factory()->create([
            'assistant_id' => $otherAssistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/appointments/my-appointments');

        $response->assertStatus(200)
                ->assertJsonCount(2, 'data');
    }
}

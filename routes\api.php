<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\AppointmentController;
use App\Http\Controllers\Api\PurchaseController;
use App\Http\Controllers\Api\BonusController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API Version 1
Route::prefix('v1')->group(function () {

    // Public routes (no authentication required)
    Route::prefix('auth')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('register', [AuthController::class, 'register']);
    });

    // Protected routes (authentication required)
    Route::middleware('auth:sanctum')->group(function () {

        // Authentication routes
        Route::prefix('auth')->group(function () {
            Route::get('user', [AuthController::class, 'user']);
            Route::post('logout', [AuthController::class, 'logout']);
            Route::post('logout-all', [AuthController::class, 'logoutAll']);
            Route::post('refresh', [AuthController::class, 'refresh']);
            Route::post('change-password', [AuthController::class, 'changePassword']);
        });

        // User routes
        Route::prefix('users')->group(function () {
            Route::get('/', [UserController::class, 'index']);
            Route::get('/{user}', [UserController::class, 'show']);
            Route::put('/{user}', [UserController::class, 'update']);
            Route::delete('/{user}', [UserController::class, 'destroy']);
            Route::get('/role/{role}', [UserController::class, 'getByRole']);
            Route::get('/{user}/stats', [UserController::class, 'getStats']);
            Route::get('/{user}/assigned-assistants', [UserController::class, 'getAssignedAssistants']);
            Route::get('/{user}/assigned-representatives', [UserController::class, 'getAssignedRepresentatives']);
        });

        // Appointment routes
        Route::prefix('appointments')->group(function () {
            Route::get('/', [AppointmentController::class, 'index']);
            Route::post('/', [AppointmentController::class, 'store']);
            Route::get('/my-appointments', [AppointmentController::class, 'myAppointments']);
            Route::get('/by-date', [AppointmentController::class, 'byDate']);
            Route::get('/{appointment}', [AppointmentController::class, 'show']);
            Route::put('/{appointment}', [AppointmentController::class, 'update']);
            Route::delete('/{appointment}', [AppointmentController::class, 'destroy']);
        });

        // Purchase routes
        Route::prefix('purchases')->group(function () {
            Route::get('/', [PurchaseController::class, 'index']);
            Route::post('/', [PurchaseController::class, 'store']);
            Route::get('/stats', [PurchaseController::class, 'getStats']);
            Route::get('/{purchase}', [PurchaseController::class, 'show']);
            Route::put('/{purchase}', [PurchaseController::class, 'update']);
            Route::delete('/{purchase}', [PurchaseController::class, 'destroy']);
            Route::get('/appointment/{appointment}', [PurchaseController::class, 'getByAppointment']);
        });

        // Bonus routes
        Route::prefix('bonuses')->group(function () {
            Route::get('/', [BonusController::class, 'index']);
            Route::post('/', [BonusController::class, 'store']);
            Route::get('/my-bonuses', [BonusController::class, 'myBonuses']);
            Route::get('/stats', [BonusController::class, 'getStats']);
            Route::get('/{bonus}', [BonusController::class, 'show']);
            Route::put('/{bonus}', [BonusController::class, 'update']);
            Route::delete('/{bonus}', [BonusController::class, 'destroy']);
            Route::get('/user/{user}', [BonusController::class, 'getByUser']);
            Route::get('/appointment/{appointment}', [BonusController::class, 'getByAppointment']);
        });
    });
});

// Legacy route for backward compatibility
Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

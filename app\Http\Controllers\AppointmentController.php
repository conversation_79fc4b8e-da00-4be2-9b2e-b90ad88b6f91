<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Bonus;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class AppointmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // dd($request->all());
        try {
            $validated = $request->validate([
                'assistant_id' => 'required',
                'representative_id' => 'required',
                'clientName' => 'required|string|max:255',
                'clientPhone' => 'required|string|max:20',
                'clientAddress' => 'required|string|max:255',
                'source' => 'required',
                'dateTime' => 'required',
                'notes' => 'nullable|string',
                'itemsCollection' => 'array',
                'appointment_type' => 'required',
            ]);

            $appointment = Appointment::create($validated);

            // ✅ 1. Bonus: outbound + announced = 5€
            // if (
            //     $validated['appointment_type'] === 'announced' &&
            //     $validated['source'] === 'outbound'
            // ) {
            //     Bonus::create([
            //         'user_id' => $validated['assistant_id'],
            //         'appointment_id' => $appointment->id,
            //         'amount' => 5,
            //         'type' => 'STANDARD',
            //         'reason' => 'sortant + annoncé',
            //     ]);
            // }

            // // ✅ 2. Bonus: LeBonCoin + purchase milestone (daily)
            // $todayStart = Carbon::today()->startOfDay();
            // $todayEnd = Carbon::today()->endOfDay();

            // $leboncoinCount = Appointment::where('source', 'leboncoin')
            //     ->where('assistant_id', $validated['assistant_id'])
            //     ->whereBetween('created_at', [$todayStart, $todayEnd])
            //     ->count();

            // $bonusAmount = match (true) {
            //     $leboncoinCount === 6 => 10,
            //     $leboncoinCount === 7 => 15,
            //     $leboncoinCount >= 8 => 20,
            //     default => 0,
            // };

            // if ($bonusAmount > 0) {
            //     $existingBonus = Bonus::where('user_id', $validated['assistant_id'])
            //         ->where('type', 'DAILY_SUPP')
            //         ->whereDate('created_at', Carbon::today())
            //         ->first();

            //     if ($existingBonus) {
            //         if ($bonusAmount > $existingBonus->amount) {
            //             $existingBonus->update([
            //                 'amount' => $bonusAmount,
            //                 'appointment_id' => $appointment->id,
            //                 'reason' => "Étape LBC : {$leboncoinCount} rendez-vous LeBonCoin aujourd'hui",
            //             ]);
            //         }
            //     } else if ($bonusAmount > 0) {
            //         Bonus::create([
            //             'user_id' => $validated['assistant_id'],
            //             'appointment_id' => $appointment->id,
            //             'amount' => $bonusAmount,
            //             'type' => 'DAILY_SUPP',
            //             'reason' => "Étape LBC : {$leboncoinCount} rendez-vous LeBonCoin aujourd'hui",
            //         ]);
            //     }
            // }

            return back()->with('success', 'Rendez-vous créé avec succès');
        } catch (\Exception $e) {
            Log::error('Erreur lors de la création du rendez-vous', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors de la création du rendez-vous');
        }
    }


    /**
     * Display the specified resource.
     */
    public function show(Appointment $appointment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Appointment $appointment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Appointment $appointment)
    {
        try {
            $validated = $request->validate([
                'assistant_id' => 'required',
                'representative_id' => 'required',
                'clientName' => 'required|string|max:255',
                'clientPhone' => 'required|string|max:20',
                'clientAddress' => 'required|string|max:255',
                'source' => 'required',
                'dateTime' => 'required',
                'notes' => 'nullable|string',
                'itemsCollection' => 'array',
                'appointment_type' => 'required',
            ]);

            $appointment->update($validated);

            // // ❌ Delete previous STANDARD bonus
            // Bonus::where('appointment_id', $appointment->id)->where('type', 'STANDARD')->delete();

            // // ✅ 1. Standard outbound + announced = 5€
            // if (
            //     $validated['appointment_type'] === 'announced' &&
            //     $validated['source'] === 'outbound'
            // ) {
            //     Bonus::create([
            //         'user_id' => $validated['assistant_id'],
            //         'appointment_id' => $appointment->id,
            //         'amount' => 5,
            //         'type' => 'STANDARD',
            //         'reason' => 'sortant + annoncé',
            //     ]);
            // }

            // // ✅ 2. LBC milestone (daily) bonus calculation
            // $todayStart = Carbon::today()->startOfDay();
            // $todayEnd = Carbon::today()->endOfDay();

            // $leboncoinCount = Appointment::where('assistant_id', $validated['assistant_id'])
            //     ->where('source', 'leboncoin')
            //     ->whereBetween('created_at', [$todayStart, $todayEnd])
            //     ->count();

            // $bonusAmount = match (true) {
            //     $leboncoinCount < 6 => 0,
            //     $leboncoinCount === 6 => 10,
            //     $leboncoinCount === 7 => 15,
            //     $leboncoinCount >= 8 => 20,
            //     default => 0,
            // };

            // // Check if bonus already exists today
            // $existingBonus = Bonus::where('user_id', $validated['assistant_id'])
            //     ->where('type', 'DAILY_SUPP')
            //     ->whereDate('created_at', Carbon::today())
            //     ->first();

            // if ($existingBonus) {
            //     // Update if the new amount is greater
            //     if ($bonusAmount > 0) {
            //         $existingBonus->update([
            //             'amount' => $bonusAmount,
            //             'appointment_id' => $appointment->id,
            //             'reason' => "Étape LBC : {$leboncoinCount} rendez-vous LeBonCoin aujourd'hui",
            //         ]);
            //     } else {
            //         $existingBonus->delete();
            //     }
            // } else if ($bonusAmount > 0) {
            //     // No bonus yet → create it
            //     Bonus::create([
            //         'user_id' => $validated['assistant_id'],
            //         'appointment_id' => $appointment->id,
            //         'amount' => $bonusAmount,
            //         'type' => 'DAILY_SUPP',
            //         'reason' => "Étape LBC : {$leboncoinCount} rendez-vous LeBonCoin aujourd'hui",
            //     ]);
            // }

            return back()->with('success', 'Rendez-vous modifié avec succès');
        } catch (\Exception $e) {
            Log::error('Erreur lors de la modification du rendez-vous', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors de la modification du rendez-vous');
        }
    }

    public function destroy(Appointment $appointment)
    {
        try {
            // $assistantId = $appointment->assistant_id;
            // $isLBC = $appointment->source === 'leboncoin';

            // if (
            //     $appointment->appointment_type === 'announced' &&
            //     $appointment->source === 'outbound'
            // ) {
            //     $bonus = Bonus::where("appointment_id",$appointment->id)
            //     ->where("reason","sortant + annoncé")->first();
            //     if ($bonus) {
            //         $bonus->delete();
            //     }
            // }

            $appointment->delete();

            // // 🔁 Recalculate daily milestone bonus if needed
            // if ($isLBC) {
            //     $todayStart = Carbon::today()->startOfDay();
            //     $todayEnd = Carbon::today()->endOfDay();

            //     $dailyCount = Appointment::where('source', 'leboncoin')
            //         ->where('assistant_id', $assistantId)
            //         ->whereBetween('created_at', [$todayStart, $todayEnd])
            //         ->count();

            //     $bonusAmount = match (true) {
            //         $dailyCount < 6 => 0,
            //         $dailyCount === 6 => 10,
            //         $dailyCount === 7 => 15,
            //         $dailyCount >= 8 => 20,
            //         default => 0,
            //     };

            //     $existingBonus = Bonus::where('user_id', $assistantId)
            //         ->where('type', 'DAILY_SUPP')
            //         ->whereDate('created_at', Carbon::today())
            //         ->first();

            //     if ($existingBonus) {
            //         if ($bonusAmount > 0) {
            //             // Update to new value
            //             $existingBonus->update([
            //                 'amount' => $bonusAmount,
            //                 'reason' => "Étape LBC : {$dailyCount} rendez-vous LeBonCoin ce jour",
            //             ]);
            //         } else {
            //             $existingBonus->delete();
            //         }
            //     }
            // }

            return back()->with('success', 'Rendez-vous supprimé avec succès');
        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression du rendez-vous', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors de la suppression du rendez-vous');
        }
    }

    public function byDate(Request $request)
    {
        try {
            $request->validate([
                'dateFilter' => 'required|date',
            ]);

            $appointmentsInDate = Appointment::whereDate('created_at', $request->dateFilter)
                ->where('assistant_id', Auth::id())
                ->orderBy('created_at', 'desc')
                ->get();

            $user = Auth::user();

            // Get only assigned representatives for this assistant
            $salesReps = $user->assignedRepresentatives;

            return Inertia::render('AssistantPages/appointmentsHistory', [
                'appsInDate' => $appointmentsInDate,
                'selectedDate' => $request->dateFilter,
                'salesReps' => $salesReps
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des rendez-vous par date', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement des rendez-vous');
        }
    }

    public function getReps()
    {
        try {
            $user = Auth::user();

            // Get only assigned representatives for this assistant
            $salesReps = $user->assignedRepresentatives;

            return Inertia::render('AssistantPages/appointmentCreation', [
                'salesReps' => $salesReps,
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des représentants', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement des représentants');
        }
    }

    public function performencePage()
    {
        try {
            $user = Auth::user();
            $weekStart = Carbon::today()->startOfWeek();
            $weekEnd = Carbon::today()->endOfWeek();

            $weeklyBonuses = Bonus::where('user_id', $user->id)
                ->whereBetween('created_at', [$weekStart, $weekEnd])
                ->sum('amount');
            $dailyBonuses = Bonus::where('user_id', $user->id)
                ->whereDate("created_at", Carbon::today())
                ->sum("amount");

            return Inertia::render('AssistantPages/assistantPerformance', [
                'weeklyBonuses' => $weeklyBonuses,
                'dailyBonuses' => $dailyBonuses
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des performances', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement des performances');
        }
    }
}

[2025-08-19 15:33:55] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, 36f69d4eabf8038934e2e068a467bd64c85fc4f46cae504de9951843202df7a7, ["*"], ?, 2, App\Models\User, 2025-08-19 15:33:55, 2025-08-19 15:33:55)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, 36f69d4eabf8038934e2e068a467bd64c85fc4f46cae504de9951843202df7a7, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 15:33:55, 2025-08-19 15:33:55)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 15:33:58] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, edd7191671d793b57a73a9a73ca209b253c4752f3b27783300036be8282046ea, ["*"], ?, 2, App\Models\User, 2025-08-19 15:33:58, 2025-08-19 15:33:58)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, edd7191671d793b57a73a9a73ca209b253c4752f3b27783300036be8282046ea, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 15:33:58, 2025-08-19 15:33:58)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 15:34:00] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, 9164bf496923761dd93f31e07e2fd6d488fe04b70b4e93c98e79a0383ed9ed3d, ["*"], ?, 2, App\Models\User, 2025-08-19 15:34:00, 2025-08-19 15:34:00)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, 9164bf496923761dd93f31e07e2fd6d488fe04b70b4e93c98e79a0383ed9ed3d, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 15:34:00, 2025-08-19 15:34:00)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 15:34:02] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, b0c30a16b6c81c0ef84cb0e05eb5520fe17f12d88a4ab3a452e5b930c1053ae6, ["*"], ?, 2, App\Models\User, 2025-08-19 15:34:02, 2025-08-19 15:34:02)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, b0c30a16b6c81c0ef84cb0e05eb5520fe17f12d88a4ab3a452e5b930c1053ae6, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 15:34:02, 2025-08-19 15:34:02)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 15:34:20] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, d8bac03d2f08e0d2476aa2658fed149fe93e1fd02e2acef337e3d143040f0678, ["*"], ?, 2, App\Models\User, 2025-08-19 15:34:20, 2025-08-19 15:34:20)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, d8bac03d2f08e0d2476aa2658fed149fe93e1fd02e2acef337e3d143040f0678, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 15:34:20, 2025-08-19 15:34:20)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 15:34:37] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, ca9daf77d8de16a17eb946a83c89a9145f941b227fe585a4c174c2c852eb179e, ["*"], ?, 1, App\Models\User, 2025-08-19 15:34:37, 2025-08-19 15:34:37)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, ca9daf77d8de16a17eb946a83c89a9145f941b227fe585a4c174c2c852eb179e, [\"*\"], ?, 1, App\\Models\\User, 2025-08-19 15:34:37, 2025-08-19 15:34:37)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 15:47:39] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, 336d5e9b6b5133ee6162a452732e648ef8a88d9ee4ce8197b8ed26de6819eaed, ["*"], ?, 2, App\Models\User, 2025-08-19 15:47:39, 2025-08-19 15:47:39)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, 336d5e9b6b5133ee6162a452732e648ef8a88d9ee4ce8197b8ed26de6819eaed, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 15:47:39, 2025-08-19 15:47:39)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 15:48:58] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, bd74a745bed0d4db848265675f4f74b4c3d4f779f033bc4d44540c5680b1e557, ["*"], ?, 2, App\Models\User, 2025-08-19 15:48:58, 2025-08-19 15:48:58)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, bd74a745bed0d4db848265675f4f74b4c3d4f779f033bc4d44540c5680b1e557, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 15:48:58, 2025-08-19 15:48:58)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 16:15:34] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, 12d30bc8394a3fa88ec3b1807d1ca903adcb42d57421b6f88f796fde2eb04710, ["*"], ?, 2, App\Models\User, 2025-08-19 16:15:34, 2025-08-19 16:15:34)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, 12d30bc8394a3fa88ec3b1807d1ca903adcb42d57421b6f88f796fde2eb04710, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 16:15:34, 2025-08-19 16:15:34)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 16:19:11] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, b140c5e32300d20e111f41ac4cc7b9512dcdad8e96eb5717cd8f88c611cf33d2, ["*"], ?, 2, App\Models\User, 2025-08-19 16:19:11, 2025-08-19 16:19:11)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, b140c5e32300d20e111f41ac4cc7b9512dcdad8e96eb5717cd8f88c611cf33d2, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 16:19:11, 2025-08-19 16:19:11)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-19 16:24:20] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (mobile-app, ee1e5c8de7bc61de924df4154693e67de47e53c42571c2c1c949b35b157e74f1, ["*"], ?, 2, App\Models\User, 2025-08-19 16:24:20, 2025-08-19 16:24:20)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (mobile-app, ee1e5c8de7bc61de924df4154693e67de47e53c42571c2c1c949b35b157e74f1, [\"*\"], ?, 2, App\\Models\\User, 2025-08-19 16:24:20, 2025-08-19 16:24:20)) at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:564)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(564): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():559}()
#2 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1434): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1399): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1238): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->login()
#17 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}()
#21 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#22 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#24 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}()
#31 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#34 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#37 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#39 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#41 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#43 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#45 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#47 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}()
#49 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-08-22 10:00:00] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\composer\\ClassLoader.php:429)
[stacktrace]
#0 {main}
"} 
[2025-08-23 08:35:58] local.ERROR: Maximum execution time of 30 seconds exceeded {"userId":2,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\composer\\ClassLoader.php:429)
[stacktrace]
#0 {main}
"} 
[2025-08-24 11:12:48] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\composer\\ClassLoader.php:429)
[stacktrace]
#0 {main}
"} 
[2025-08-24 11:13:19] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\SQLiteGrammar.php:10)
[stacktrace]
#0 {main}
"} 
[2025-08-25 11:55:28] local.ERROR: Maximum execution time of 30 seconds exceeded {"userId":2,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\Coding\\Freelence\\AS-shop\\vendor\\composer\\ClassLoader.php:429)
[stacktrace]
#0 {main}
"} 
[2025-08-25 12:50:18] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 
[2025-08-25 12:50:34] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 
[2025-08-25 12:51:20] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 
[2025-08-25 12:51:53] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 
[2025-08-25 12:51:55] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 
[2025-08-25 12:52:19] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 
[2025-08-25 12:52:28] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 
[2025-08-25 12:53:26] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 
[2025-08-25 12:53:33] local.ERROR: Error loading appointments by date {"error":"The date filter field is required."} 

import React, { useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm, usePage } from '@inertiajs/react';
import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Calendar, Clock, User, Phone, Filter, Pencil, Trash2, Eye } from "lucide-react"
import AppointmentEditForm from '../../components/appointmentEditForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import dayjs from 'dayjs';

const breadcrumbs = [
    {
        title: 'Historique des rendez-vous',
        href: '/appointmentsHistory',
    },
];

export default function AppointmentsHistory() {

    const { appsInDate, selectedDate, salesReps } = usePage().props;
    const { delete: destroy } = useForm();
    const [dateFilter, setDateFilter] = useState("")

    const filterByDate = () => {
        router.get('/appointmentsHistory/by-date', { dateFilter });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case "announced":
                return "bg-green-100 text-green-800"
            case "not_announced":
                return "bg-red-100 text-red-800"
            default:
                return "bg-gray-100 text-gray-800"
        }
    }


    // ? Screen Size

    const [width, setWidth] = useState(window.innerWidth)

    useEffect(() => {
        const handleResize = () => {
            setWidth(window.innerWidth)
        };

        window.addEventListener("resize", handleResize)
        return () => window.removeEventListener("resize", handleResize)
    }, [])


    // & Appointment Edit and Delete
    const [editingAppointment, setEditingAppointment] = useState(null)
    const [deletingAppointment, setDeletingAppointment] = useState(null)

    const handleDeleteAppointment = (appointment) => {
        if (updatable(appointment)) {
            destroy(route("appointments.destroy", appointment.id), {
                onSuccess: () => setDeletingAppointment(null)
            })
        } else {
            alert("vous ne pouvez plus supprimer le rendez-vous")
        }
    }

    // ? Updatable or not 

    const updatable = (appointment) => {
        const now = dayjs();
        const appDateTime = dayjs(appointment.dateTime);
        const createdAt = dayjs(appointment.created_at);

        const isBeforeTwoHours = now.isBefore(appDateTime.subtract(2, 'hour'));
        const isSameCreationDay = createdAt.isSame(now, 'day');

        return isBeforeTwoHours && isSameCreationDay;
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="AppointmentsHistory" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="space-y-6">
                    <div>
                        <h1 className="text-3xl font-bold text-[#525e62]">Historique des rendez-vous</h1>
                        <p className="text-[#525e62]/70">Consultez l'historique complet de vos rendez-vous créés.</p>
                    </div>

                    {/* Appointment Details */}
                    <Card className="border-0 shadow-lg">
                        <CardHeader>
                            <CardTitle className="text-[#525e62] flex items-center justify-between">
                                <span className="flex items-center">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    Journal complet des rendez-vous
                                </span>
                                <div className="flex items-center space-x-2">
                                    <Filter className="h-4 w-4 text-[#525e62]/70" />
                                    <span className="text-sm text-[#525e62]/70">Filtres</span>
                                </div>
                            </CardTitle>

                            {/* Filters */}
                            <div className="flex flex-col sm:flex-row gap-3 mt-4">
                                <Input
                                    type="date"
                                    value={dateFilter ? dateFilter : selectedDate}
                                    onChange={(e) => {
                                        setDateFilter(e.target.value);
                                    }}
                                    className="border-[#525e62]/20 focus:border-[#525e62]"
                                    placeholder="Filtrer par date"
                                />
                                <Button
                                    variant="outline"
                                    onClick={filterByDate}
                                    className="border-[#525e62] text-[#525e62]"
                                >
                                    Rechercher
                                </Button>
                            </div>

                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3 max-h-[600px] overflow-y-auto">
                                {appsInDate && appsInDate[0] ? appsInDate.map((appointment) => (
                                    <div
                                        key={appointment.id}
                                        className="p-4 border border-[#525e62]/10 rounded-lg  hover:bg-[#f1efe0]/30 transition-colors"
                                    >
                                        <div className="flex items-start justify-between mb-3">
                                            <div className="flex items-center space-x-3">
                                                <User className="h-4 w-4 text-[#525e62]/70" />
                                                <span className="font-medium text-[#525e62]">{appointment.clientName}</span>
                                            </div>
                                            <div className="flex space-x-2">
                                                <Badge className={`${getStatusColor(appointment.appointment_type)}`}>{appointment.appointment_type == "announced" ? "Annoncé" : "Non Annoncé"} + {appointment.source == "outbound" ? "Appel Sortant" : "LeBonCoin"}</Badge>
                                                {/* {appointment.appointment_type == "announced" & appointment.source == 'outbound' ?
                                                    <>
                                                        <Badge className="bg-green-100 text-green-800">+5€</Badge>
                                                    </>
                                                    :
                                                    <></>
                                                } */}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-3 md:grid-cols-4 gap-3 text-sm text-[#525e62]/70">
                                            <div className="md:flex hidden items-center space-x-2">
                                                <Phone className="h-4 w-4" />
                                                <span>{appointment.clientPhone}</span>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <Calendar className="h-4 w-4" />
                                                <span>{width >= 768 ? appointment.dateTime.slice(0, 10) : appointment.dateTime.slice(5, 10)}</span>
                                            </div>
                                            <div className="flex items-center md:justify-start justify-center space-x-2">
                                                <Clock className="h-4 w-4" />
                                                <span>{appointment.dateTime.slice(11,16)}</span>
                                            </div>
                                            <div className="flex items-center justify-end px-4 cursor-pointer">
                                                {
                                                    updatable(appointment) ?
                                                        <>
                                                            <div className="flex gap-3 ">
                                                                <span onClick={() => setEditingAppointment(appointment)} className={`w-8 h-8 flex rounded-full bg-gray-100 hover:bg-white hover:text-black transition items-center justify-center`}>
                                                                    <Pencil className="h-4 w-4" />
                                                                </span>
                                                                <span onClick={() => setDeletingAppointment(appointment)} className={`w-8 h-8 flex rounded-full bg-gray-100 hover:bg-white hover:text-black transition items-center justify-center`}>
                                                                    <Trash2 className="h-4 w-4" />
                                                                </span>
                                                            </div>
                                                        </>
                                                        :
                                                        <>
                                                            <div className="flex gap-3 ">
                                                                <span onClick={() => setEditingAppointment(appointment)} className={`w-8 h-8 flex rounded-full bg-gray-100 hover:bg-white hover:text-black transition items-center justify-center`}>
                                                                    <Eye className="h-4 w-4" />
                                                                </span>
                                                            </div>
                                                        </>
                                                }

                                            </div>
                                        </div>
                                    </div>
                                ))
                                    :
                                    <div className='flex items-center justify-center h-[20vh] w-full border-[#525e62]/10 rounded-lg  hover:bg-[#f1efe0]/30 transition-colors p-4 '>
                                        <h1>Aucun rendez-vous disponible</h1>
                                    </div>
                                }
                            </div>
                        </CardContent>
                    </Card>

                    {editingAppointment && (
                        <AppointmentEditForm
                            appointment={editingAppointment}
                            onClose={() => setEditingAppointment(null)}
                            salesReps={salesReps}
                        />
                    )}

                    {/* Delete Confirmation Modal */}
                    {deletingAppointment && (
                        <Dialog open={!!deletingAppointment} onOpenChange={() => setDeletingAppointment(null)}>
                            <DialogContent className="sm:max-w-[400px]">
                                <DialogHeader>
                                    <DialogTitle className="text-[#525e62] flex items-center">
                                        <Trash2 className="mr-2 h-5 w-5 text-red-600" />
                                        Confirmer la suppression
                                    </DialogTitle>
                                    <DialogDescription className="text-[#525e62]/70">
                                        <span className="font-medium">
                                            Êtes-vous sûr de vouloir supprimer le rendez-vous
                                            avec <span className='font-black text-black '>{deletingAppointment.clientName}</span> prévu à {deletingAppointment.dateTime.slice(11)}
                                        </span>
                                    </DialogDescription>
                                </DialogHeader>
                                <DialogFooter className="flex justify-end space-x-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => setDeletingAppointment(null)}
                                        className="border-[#525e62] text-[#525e62]"
                                    >
                                        Annuler
                                    </Button>
                                    <Button
                                        variant="destructive"
                                        onClick={() => handleDeleteAppointment(deletingAppointment)}
                                        className="bg-red-600 hover:bg-red-700 text-white"
                                    >
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Supprimer
                                    </Button>
                                </DialogFooter>
                            </DialogContent>
                        </Dialog>
                    )}
                </div>
            </div>
        </AppLayout>
    );
};

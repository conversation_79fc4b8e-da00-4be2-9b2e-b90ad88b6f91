<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserCollection;
use App\Http\Resources\UserResource;
use App\Http\Traits\ApiResponse;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of users
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $role = $request->get('role');
        $search = $request->get('search');

        $query = User::query();

        // Filter by role
        if ($role) {
            $query->where('role', $role);
        }

        // Search by name or email
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->paginate($perPage);

        return $this->paginatedResponse(
            new UserCollection($users),
            'Users retrieved successfully'
        );
    }

    /**
     * Display the specified user
     */
    public function show(User $user): JsonResponse
    {
        $user->load(['createdAppointments', 'receivedAppointments', 'bonuses']);
        
        return $this->successResponse(
            new UserResource($user),
            'User retrieved successfully'
        );
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|string|email|max:255|unique:users,email,' . $user->id,
            'role' => 'sometimes|required|string|in:assistant,representative,admin,recruiter,executive',
            'is_activated' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $user->update($request->only(['name', 'email', 'role', 'is_activated']));

        return $this->successResponse(
            new UserResource($user),
            'User updated successfully'
        );
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user): JsonResponse
    {
        $user->delete();

        return $this->successResponse(null, 'User deleted successfully');
    }

    /**
     * Get users by role
     */
    public function getByRole(string $role): JsonResponse
    {
        $validRoles = ['assistant', 'representative', 'admin', 'recruiter', 'executive'];
        
        if (!in_array($role, $validRoles)) {
            return $this->errorResponse('Invalid role specified', 400);
        }

        $users = User::where('role', $role)
                    ->where('is_activated', true)
                    ->get();

        return $this->successResponse(
            UserResource::collection($users),
            "Users with role '{$role}' retrieved successfully"
        );
    }

    /**
     * Get assistants assigned to a representative
     */
    public function getAssignedAssistants(User $representative): JsonResponse
    {
        if ($representative->role !== 'representative') {
            return $this->errorResponse('User is not a representative', 400);
        }

        $assistants = $representative->assignedAssistants;

        return $this->successResponse(
            UserResource::collection($assistants),
            'Assigned assistants retrieved successfully'
        );
    }

    /**
     * Get representatives assigned to an assistant
     */
    public function getAssignedRepresentatives(User $user): JsonResponse
    {
        if ($user->role !== 'assistant') {
            return $this->errorResponse('User is not an assistant', 400);
        }

        $representatives = $user->assignedRepresentatives;

        return $this->successResponse(
            UserResource::collection($representatives),
            'Assigned representatives retrieved successfully'
        );
    }

    /**
     * Get user statistics
     */
    public function getStats(User $user): JsonResponse
    {
        $stats = [];

        if ($user->role === 'assistant') {
            $stats = [
                'total_appointments_created' => $user->createdAppointments()->count(),
                'appointments_this_month' => $user->createdAppointments()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
                'total_bonuses' => $user->bonuses()->sum('amount'),
                'bonuses_this_month' => $user->bonuses()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->sum('amount'),
            ];
        } elseif ($user->role === 'representative') {
            $stats = [
                'total_appointments_received' => $user->receivedAppointments()->count(),
                'appointments_this_month' => $user->receivedAppointments()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
                'total_bonuses' => $user->bonuses()->sum('amount'),
                'bonuses_this_month' => $user->bonuses()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->sum('amount'),
            ];
        }

        return $this->successResponse($stats, 'User statistics retrieved successfully');
    }
}
